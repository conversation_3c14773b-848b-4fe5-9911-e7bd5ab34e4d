package com.sdwu.kotlin.viewmodel

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.sdwu.kotlin.data.model.User
import com.sdwu.kotlin.data.repository.UserRepositoryInterface
import kotlinx.coroutines.launch

/**
 * DataBinding专用ViewModel
 * 使用LiveData进行数据绑定，展示DataBinding的自动绑定功能
 * 与ProfileViewModel的区别：使用LiveData而不是StateFlow
 */
class DataBindingViewModel(
    private val userRepository: UserRepositoryInterface
) : ViewModel() {

    companion object {
        private const val TAG = "DataBindingViewModel"
    }

    // 使用LiveData进行数据绑定 - DataBinding的推荐方式
    private val _user = MutableLiveData<User?>()
    val user: LiveData<User?> = _user

    private val _isLoading = MutableLiveData(false)
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    // 输入字段的双向绑定
    val nameInput = MutableLiveData<String>()
    val emailInput = MutableLiveData<String>()

    // 计算属性 - 用于显示格式化的用户信息
    private val _userDisplayText = MutableLiveData<String>()
    val userDisplayText: LiveData<String> = _userDisplayText

    // 按钮启用状态
    private val _isSaveEnabled = MutableLiveData(true)
    val isSaveEnabled: LiveData<Boolean> = _isSaveEnabled

    init {
        Log.d(TAG, "DataBindingViewModel初始化开始")
        try {
            loadUserProfile()
            Log.d(TAG, "DataBindingViewModel初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "DataBindingViewModel初始化失败", e)
            _error.value = "初始化失败: ${e.message}"
        }
    }

    /**
     * 加载用户资料
     */
    fun loadUserProfile() {
        Log.d(TAG, "开始加载用户资料")
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            try {
                Log.d(TAG, "尝试初始化默认用户")
                userRepository.initializeDefaultUser()
                Log.d(TAG, "默认用户初始化完成")

                Log.d(TAG, "尝试获取当前用户")
                val user = userRepository.getCurrentUser()
                Log.d(TAG, "获取到用户: ${user?.name ?: "null"}")

                _user.value = user
                _isLoading.value = false

                // 更新输入字段
                user?.let {
                    nameInput.value = it.name
                    emailInput.value = it.email
                    updateUserDisplayText(it)
                }

                Log.d(TAG, "用户资料加载成功")
            } catch (e: Exception) {
                Log.e(TAG, "加载用户资料失败", e)
                _isLoading.value = false
                _error.value = "加载用户信息失败: ${e.message}"
            }
        }
    }

    /**
     * 更新用户信息
     */
    fun updateUserInfo() {
        val currentUser = _user.value ?: return
        val name = nameInput.value?.trim() ?: ""
        val email = emailInput.value?.trim() ?: ""

        // 验证输入
        val validationError = validateInput(name, email)
        if (validationError != null) {
            _error.value = validationError
            return
        }

        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            try {
                val updatedUser = currentUser.copy(
                    name = name,
                    email = email
                )

                userRepository.updateUser(updatedUser)
                _user.value = updatedUser
                _isLoading.value = false

                updateUserDisplayText(updatedUser)
                Log.d(TAG, "用户信息更新成功")

            } catch (e: Exception) {
                Log.e(TAG, "更新用户信息失败", e)
                _isLoading.value = false
                _error.value = "更新用户信息失败: ${e.message}"
            }
        }
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _error.value = null
    }

    /**
     * 刷新数据
     */
    fun refresh() {
        loadUserProfile()
    }

    /**
     * 验证用户输入
     */
    private fun validateInput(name: String, email: String): String? {
        return when {
            name.isBlank() -> "用户名不能为空"
            email.isBlank() -> "邮箱不能为空"
            !email.contains("@") -> "邮箱格式不正确"
            else -> null
        }
    }

    /**
     * 更新用户显示文本
     */
    private fun updateUserDisplayText(user: User) {
        _userDisplayText.value = "用户: ${user.name}\n邮箱: ${user.email}\n注册日期: ${user.registrationDate}"
    }

    /**
     * 更新按钮状态
     */
    fun updateButtonState() {
        val loading = _isLoading.value ?: false
        _isSaveEnabled.value = !loading
    }
}
