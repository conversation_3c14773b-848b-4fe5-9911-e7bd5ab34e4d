package com.sdwu.kotlin

import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Observer
import com.sdwu.kotlin.databinding.ActivityDataBindingBinding
import com.sdwu.kotlin.viewmodel.DataBindingViewModel

/**
 * DataBinding示例Activity
 * 展示DataBinding的自动数据绑定功能
 * 与ViewBindingActivity的区别：
 * 1. 使用DataBindingUtil.setContentView()
 * 2. 设置ViewModel到binding
 * 3. 设置lifecycleOwner用于LiveData观察
 * 4. 不需要手动设置点击监听器和UI更新
 */
class DataBindingActivity : ComponentActivity() {

    companion object {
        private const val TAG = "DataBindingActivity"
    }

    // DataBinding - 自动生成的绑定类
    private lateinit var binding: ActivityDataBindingBinding
    // ViewModel实例
    private lateinit var viewModel: DataBindingViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "DataBindingActivity onCreate开始")

        try {
            // 初始化DataBinding - 使用DataBindingUtil
            binding = DataBindingUtil.setContentView(this, R.layout.activity_data_binding)

            // 获取Application和依赖容器
            val appContainer = (application as KotlinApplication).appContainer

            // 创建ViewModel实例
            viewModel = DataBindingViewModel(appContainer.userRepository)

            // 设置DataBinding的关键配置
            setupDataBinding()

            // 设置手动点击监听器（简化版本）
            setupClickListeners()

            Log.d(TAG, "DataBinding初始化成功")

        } catch (e: Exception) {
            Log.e(TAG, "DataBinding初始化失败", e)
            // 如果DataBinding失败，显示错误信息
            setContentView(android.R.layout.simple_list_item_1)
        }
    }

    /**
     * 设置DataBinding的关键配置
     */
    private fun setupDataBinding() {
        // 1. 设置ViewModel到binding
        binding.viewModel = viewModel

        // 2. 设置lifecycleOwner用于LiveData观察
        binding.lifecycleOwner = this

        Log.d(TAG, "DataBinding配置完成")
    }

    /**
     * 设置点击监听器（简化版本）
     */
    private fun setupClickListeners() {
        binding.btnSave.setOnClickListener {
            Log.d(TAG, "保存按钮点击")
            val name = binding.etName.text.toString()
            val email = binding.etEmail.text.toString()

            if (name.isNotEmpty() && email.isNotEmpty()) {
                // 手动更新ViewModel
                viewModel.nameInput.value = name
                viewModel.emailInput.value = email
                viewModel.updateUserInfo()

                // 手动更新UI显示
                binding.tvUserInfo.text = "用户: $name\n邮箱: $email"
            } else {
                Log.w(TAG, "用户名或邮箱为空")
                binding.tvUserInfo.text = "请输入用户名和邮箱"
            }
        }

        binding.btnLoad.setOnClickListener {
            Log.d(TAG, "刷新按钮点击")
            viewModel.refresh()

            // 观察用户数据变化并更新UI
            viewModel.user.observe(this, Observer { user ->
                user?.let {
                    binding.etName.setText(it.name)
                    binding.etEmail.setText(it.email)
                    binding.tvUserInfo.text = "用户: ${it.name}\n邮箱: ${it.email}\n注册日期: ${it.registrationDate}"
                }
            })
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "DataBindingActivity onDestroy")
    }
}
