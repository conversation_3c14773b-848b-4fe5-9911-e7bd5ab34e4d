<?xml version="1.0" encoding="utf-8"?>
<!-- DataBinding布局示例 - 简化版本 -->
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="viewModel"
            type="com.sdwu.kotlin.viewmodel.DataBindingViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp"
        tools:context=".DataBindingActivity">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="DataBinding示例"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="32dp" />

        <!-- 用户名输入 -->
        <EditText
            android:id="@+id/et_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="用户名"
            android:inputType="textPersonName"
            android:layout_marginBottom="16dp" />

        <!-- 邮箱输入 -->
        <EditText
            android:id="@+id/et_email"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="邮箱"
            android:inputType="textEmailAddress"
            android:layout_marginBottom="24dp" />

        <!-- 用户信息显示 -->
        <TextView
            android:id="@+id/tv_user_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="用户信息将在这里显示"
            android:textSize="16sp"
            android:background="@android:color/darker_gray"
            android:textColor="@android:color/white"
            android:padding="12dp"
            android:layout_marginBottom="24dp" />

        <!-- 按钮容器 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- 保存按钮 -->
            <Button
                android:id="@+id/btn_save"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="保存"
                android:layout_marginEnd="8dp" />

            <!-- 刷新按钮 -->
            <Button
                android:id="@+id/btn_load"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="刷新"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- 说明文本 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="这是DataBinding示例（简化版本）\n• 使用&lt;layout&gt;标签包装\n• 支持数据绑定表达式\n• 自动观察LiveData变化"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginTop="24dp"
            android:padding="8dp"
            android:background="@android:color/background_light" />

    </LinearLayout>
</layout>
