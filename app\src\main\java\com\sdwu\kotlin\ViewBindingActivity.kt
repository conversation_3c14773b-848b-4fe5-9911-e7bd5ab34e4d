package com.sdwu.kotlin

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.activity.ComponentActivity
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.repeatOnLifecycle
import com.sdwu.kotlin.databinding.ActivityViewBindingBinding
import com.sdwu.kotlin.viewmodel.ProfileViewModel
import kotlinx.coroutines.launch

/**
 * ViewBinding示例Activity
 * 展示ViewBinding的使用方法（DataBinding的简化版本）
 */
class ViewBindingActivity : ComponentActivity() {

    companion object {
        private const val TAG = "ViewBindingActivity"
    }

    // ViewBinding - 自动生成的绑定类
    private lateinit var binding: ActivityViewBindingBinding
    // ViewModel实例
    private lateinit var viewModel: ProfileViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "ViewBindingActivity onCreate开始")

        try {
            // 初始化ViewBinding
            binding = ActivityViewBindingBinding.inflate(layoutInflater)
            setContentView(binding.root)
            
            // 获取Application和依赖容器
            val appContainer = (application as KotlinApplication).appContainer
            
            // 创建ViewModel实例
            viewModel = ProfileViewModel(appContainer.userRepository)
            
            // 设置点击事件
            setupClickListeners()
            
            // 观察ViewModel状态
            observeViewModel()
            
            Log.d(TAG, "ViewBinding初始化成功")
            
        } catch (e: Exception) {
            Log.e(TAG, "ViewBinding初始化失败", e)
        }
    }

    /**
     * 设置点击事件监听器
     */
    private fun setupClickListeners() {
        binding.btnSave.setOnClickListener {
            val name = binding.etName.text.toString()
            val email = binding.etEmail.text.toString()

            if (name.isNotEmpty() && email.isNotEmpty()) {
                viewModel.updateUserInfo(name, email)
            } else {
                Log.w(TAG, "用户名或邮箱为空")
            }
        }

        binding.btnLoad.setOnClickListener {
            viewModel.loadUserProfile()
        }
    }

    /**
     * 观察ViewModel状态变化
     * 手动更新UI（因为没有DataBinding的自动绑定）
     */
    private fun observeViewModel() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.uiState.collect { uiState ->
                    Log.d(TAG, "UI状态更新: $uiState")

                    // 更新用户数据
                    uiState.user?.let { user ->
                        binding.etName.setText(user.name)
                        binding.etEmail.setText(user.email)
                        binding.tvUserInfo.text = "用户: ${user.name}\n邮箱: ${user.email}"
                    }

                    // 更新加载状态
                    binding.progressBar.visibility = if (uiState.isLoading) {
                        View.VISIBLE
                    } else {
                        View.GONE
                    }

                    binding.btnSave.isEnabled = !uiState.isLoading
                    binding.btnLoad.isEnabled = !uiState.isLoading

                    // 更新错误信息
                    if (uiState.error != null) {
                        binding.tvError.text = uiState.error
                        binding.tvError.visibility = View.VISIBLE
                    } else {
                        binding.tvError.visibility = View.GONE
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "ViewBindingActivity onDestroy")
    }
}
