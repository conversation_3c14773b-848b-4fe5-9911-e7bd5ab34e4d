package com.sdwu.kotlin

import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.ProgressBar
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 传统View系统示例
 * 展示命令式UI的特点和View层级结构
 */
class TraditionalViewActivity : ComponentActivity() {

    companion object {
        private const val TAG = "TraditionalViewActivity"
    }

    // 传统方式：需要声明所有View引用
    private lateinit var titleTextView: TextView
    private lateinit var contentTextView: TextView
    private lateinit var loadButton: Button
    private lateinit var saveButton: Button
    private lateinit var progressBar: ProgressBar
    private lateinit var errorTextView: TextView

    // 状态变量：需要手动管理
    private var isLoading = false
    private var hasError = false
    private var userData: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 设置布局
        setContentView(R.layout.activity_traditional_view)
        
        // 初始化View引用 - 需要手动findViewById
        initViews()
        
        // 设置初始状态
        setupInitialState()
        
        // 设置事件监听器
        setupClickListeners()
        
        Log.d(TAG, "传统View系统Activity初始化完成")
    }

    /**
     * 初始化所有View引用
     * 传统方式：需要手动findViewById每个View
     */
    private fun initViews() {
        titleTextView = findViewById(R.id.tv_title)
        contentTextView = findViewById(R.id.tv_content)
        loadButton = findViewById(R.id.btn_load)
        saveButton = findViewById(R.id.btn_save)
        progressBar = findViewById(R.id.progress_bar)
        errorTextView = findViewById(R.id.tv_error)
        
        Log.d(TAG, "所有View引用初始化完成")
    }

    /**
     * 设置初始UI状态
     * 命令式：需要明确告诉每个View应该显示什么
     */
    private fun setupInitialState() {
        titleTextView.text = "传统View系统示例"
        contentTextView.text = "点击加载按钮获取数据"
        loadButton.text = "加载数据"
        saveButton.text = "保存数据"
        saveButton.isEnabled = false
        progressBar.visibility = View.GONE
        errorTextView.visibility = View.GONE
        
        Log.d(TAG, "初始UI状态设置完成")
    }

    /**
     * 设置点击事件监听器
     */
    private fun setupClickListeners() {
        loadButton.setOnClickListener {
            if (!isLoading) {
                loadData()
            }
        }

        saveButton.setOnClickListener {
            if (!isLoading && userData != null) {
                saveData()
            }
        }
    }

    /**
     * 模拟加载数据
     * 展示命令式UI如何手动管理状态变化
     */
    private fun loadData() {
        Log.d(TAG, "开始加载数据")
        
        // 手动更新UI状态 - 开始加载
        updateLoadingState(true)
        
        lifecycleScope.launch {
            try {
                // 模拟网络请求
                delay(2000)
                
                // 模拟成功获取数据
                userData = "用户数据已加载\n姓名: 张三\n邮箱: <EMAIL>"
                
                // 手动更新UI状态 - 加载成功
                updateSuccessState(userData!!)
                
            } catch (e: Exception) {
                Log.e(TAG, "加载数据失败", e)
                
                // 手动更新UI状态 - 加载失败
                updateErrorState("加载数据失败: ${e.message}")
            }
        }
    }

    /**
     * 模拟保存数据
     */
    private fun saveData() {
        Log.d(TAG, "开始保存数据")
        
        updateLoadingState(true)
        
        lifecycleScope.launch {
            try {
                delay(1000)
                
                // 模拟保存成功
                updateSuccessState("数据保存成功！\n$userData")
                
            } catch (e: Exception) {
                Log.e(TAG, "保存数据失败", e)
                updateErrorState("保存数据失败: ${e.message}")
            }
        }
    }

    /**
     * 更新加载状态
     * 命令式：需要手动更新每个相关的View
     */
    private fun updateLoadingState(loading: Boolean) {
        isLoading = loading
        hasError = false
        
        // 手动更新每个View的状态
        if (loading) {
            progressBar.visibility = View.VISIBLE
            loadButton.isEnabled = false
            saveButton.isEnabled = false
            loadButton.text = "加载中..."
            errorTextView.visibility = View.GONE
        } else {
            progressBar.visibility = View.GONE
            loadButton.isEnabled = true
            loadButton.text = "加载数据"
            saveButton.isEnabled = userData != null
        }
        
        Log.d(TAG, "加载状态更新: loading=$loading")
    }

    /**
     * 更新成功状态
     */
    private fun updateSuccessState(content: String) {
        isLoading = false
        hasError = false
        
        // 手动更新UI显示成功状态
        progressBar.visibility = View.GONE
        contentTextView.text = content
        loadButton.isEnabled = true
        loadButton.text = "重新加载"
        saveButton.isEnabled = true
        errorTextView.visibility = View.GONE
        
        Log.d(TAG, "成功状态更新")
    }

    /**
     * 更新错误状态
     */
    private fun updateErrorState(error: String) {
        isLoading = false
        hasError = true
        
        // 手动更新UI显示错误状态
        progressBar.visibility = View.GONE
        errorTextView.text = error
        errorTextView.visibility = View.VISIBLE
        loadButton.isEnabled = true
        loadButton.text = "重试"
        saveButton.isEnabled = false
        
        Log.d(TAG, "错误状态更新: $error")
    }

    /**
     * 演示View层级遍历
     */
    private fun demonstrateViewHierarchy() {
        Log.d(TAG, "=== View层级结构演示 ===")
        
        // 获取根View
        val rootView = findViewById<View>(android.R.id.content)
        
        // 递归遍历View树
        traverseViewHierarchy(rootView, 0)
    }

    /**
     * 递归遍历View层级
     */
    private fun traverseViewHierarchy(view: View, depth: Int) {
        val indent = "  ".repeat(depth)
        val viewInfo = "${view.javaClass.simpleName} (id: ${view.id})"
        Log.d(TAG, "$indent$viewInfo")
        
        // 如果是ViewGroup，继续遍历子View
        if (view is android.view.ViewGroup) {
            for (i in 0 until view.childCount) {
                traverseViewHierarchy(view.getChildAt(i), depth + 1)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // 演示View层级遍历
        demonstrateViewHierarchy()
    }
}
