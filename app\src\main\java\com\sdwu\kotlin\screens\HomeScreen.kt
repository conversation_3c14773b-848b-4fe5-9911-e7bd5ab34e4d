package com.sdwu.kotlin.screens

import android.util.Log
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.sdwu.kotlin.KotlinApplication
import com.sdwu.kotlin.viewmodel.HomeViewModel
import com.sdwu.kotlin.utils.ErrorLogger
import com.sdwu.kotlin.utils.NavigationErrorHandler
import com.sdwu.kotlin.utils.ComposeNavigationHelper
import com.sdwu.kotlin.components.SafeNavigationButton
import android.content.Intent
import com.sdwu.kotlin.TraditionalViewActivity
import com.sdwu.kotlin.ViewBindingActivity
import com.sdwu.kotlin.DataBindingActivity

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(navController: NavController) {
    // 页面加载监控
    ComposeNavigationHelper.MonitorPageLoad(
        pageName = "HomeScreen",
        onLoadStart = { Log.d("HomeScreen", "首页开始加载") },
        onLoadComplete = { Log.d("HomeScreen", "首页加载完成") },
        onLoadError = { e -> Log.e("HomeScreen", "首页加载失败", e) }
    )

    // 导航状态监控
    ComposeNavigationHelper.MonitorNavigationState(navController, "HomeScreen")

    val context = LocalContext.current
    val appContainer = (context.applicationContext as KotlinApplication).appContainer

    // 创建ViewModel实例
    val viewModel: HomeViewModel = viewModel {
        HomeViewModel(appContainer.homeRepository)
    }

    // 收集UI状态
    val uiState by viewModel.uiState.collectAsState()
    val searchQuery by viewModel.searchQuery.collectAsState()

    // 显示错误信息
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            Log.e("HomeScreen", "UI状态错误: $error")
            ErrorLogger.logError("HomeScreen", "UI状态错误", Exception(error))
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 标题和刷新按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "首页",
                style = MaterialTheme.typography.headlineMedium
            )

            IconButton(onClick = { viewModel.refreshData() }) {
                Icon(Icons.Default.Refresh, contentDescription = "刷新")
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 搜索框
        OutlinedTextField(
            value = searchQuery,
            onValueChange = { viewModel.searchItems(it) },
            label = { Text("搜索项目") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )

        Spacer(modifier = Modifier.height(16.dp))

        // 导航按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            // 个人资料按钮
            var profileNavigating by remember { mutableStateOf(false) }
            var profileError by remember { mutableStateOf<String?>(null) }

            Button(
                onClick = {
                    if (!profileNavigating) {
                        profileNavigating = true
                        profileError = null

                        try {
                            Log.d("HomeScreen", "=== 开始导航到个人资料 ===")
                            Log.d("HomeScreen", "NavController状态: ${navController.currentDestination?.route}")

                            // 检查NavController是否有效
                            val currentRoute = navController.currentDestination?.route
                            Log.d("HomeScreen", "当前路由: $currentRoute")

                            // 检查导航图
                            val graph = navController.graph
                            Log.d("HomeScreen", "导航图起始目的地: ${graph.startDestinationRoute}")

                            // 检查目标路由是否存在
                            val profileDestination = graph.findNode("profile")
                            Log.d("HomeScreen", "Profile路由是否存在: ${profileDestination != null}")

                            val success = NavigationErrorHandler.safeNavigateTo(
                                navController = navController,
                                route = "profile",
                                from = "home"
                            )

                            Log.d("HomeScreen", "导航结果: success = $success")

                            if (!success) {
                                val errorMsg = "导航到个人资料失败"
                                Log.e("HomeScreen", errorMsg)
                                profileError = errorMsg
                            } else {
                                Log.d("HomeScreen", "导航成功，等待页面加载...")
                            }

                        } catch (e: Exception) {
                            val errorMsg = "导航过程中发生异常: ${e.message}"
                            Log.e("HomeScreen", errorMsg, e)
                            ErrorLogger.logError("HomeScreen", "导航异常", e)
                            profileError = errorMsg
                        } finally {
                            profileNavigating = false
                            Log.d("HomeScreen", "=== 导航操作完成 ===")
                        }
                    }
                },
                enabled = !profileNavigating
            ) {
                if (profileNavigating) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }
                Text("个人资料")
            }

            // 显示错误信息
            profileError?.let { error ->
                LaunchedEffect(error) {
                    Log.e("HomeScreen", "Profile导航错误: $error")
                    // 3秒后清除错误信息
                    kotlinx.coroutines.delay(3000)
                    profileError = null
                }
            }

            // 设置按钮
            var settingsNavigating by remember { mutableStateOf(false) }
            Button(
                onClick = {
                    if (!settingsNavigating) {
                        settingsNavigating = true
                        val success = NavigationErrorHandler.safeNavigateTo(
                            navController = navController,
                            route = "settings",
                            from = "home"
                        )
                        if (!success) {
                            Log.e("HomeScreen", "导航到设置失败")
                        }
                        settingsNavigating = false
                    }
                },
                enabled = !settingsNavigating
            ) {
                if (settingsNavigating) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }
                Text("设置")
            }

            // ViewBinding示例按钮
            Button(
                onClick = {
                    val intent = Intent(context, ViewBindingActivity::class.java)
                    context.startActivity(intent)
                }
            ) {
                Text("ViewBinding示例")
            }

            // DataBinding示例按钮
            Button(
                onClick = {
                    val intent = Intent(context, DataBindingActivity::class.java)
                    context.startActivity(intent)
                }
            ) {
                Text("DataBinding示例")
            }

            // 传统View系统示例按钮
            Button(
                onClick = {
                    val intent = Intent(context, TraditionalViewActivity::class.java)
                    context.startActivity(intent)
                }
            ) {
                Text("传统View系统示例")
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 加载状态
        if (uiState.isLoading) {
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            // 列表项
            LazyColumn {
                items(uiState.items) { item ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        onClick = {
                            val success = NavigationErrorHandler.safeNavigateTo(
                                navController = navController,
                                route = "detail/${item.id}",
                                from = "home"
                            )
                            if (!success) {
                                Log.e("HomeScreen", "导航到详情页面失败，itemId: ${item.id}")
                            }
                        }
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = item.title,
                                style = MaterialTheme.typography.bodyLarge,
                                modifier = Modifier.padding(bottom = 4.dp)
                            )
                            Text(
                                text = item.description,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
    }
}
