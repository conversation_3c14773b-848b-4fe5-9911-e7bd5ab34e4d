pluginManagement {
    repositories {
        // AliRepo 阿里仓库服务 https://maven.aliyun.com/mvn/view
        maven {
            url = uri("https://maven.aliyun.com/repository/public")
        }
        maven {
            url = uri("https://maven.aliyun.com/repository/google")
        }
        maven {
            url = uri("https://maven.aliyun.com/repository/gradle-plugin")
        }
        maven {
            url = uri("http://10.1.3.144:8081/repository/airdoc-snapshot/")
            isAllowInsecureProtocol = true
        }
        maven {
            url = uri("http://10.1.3.144:8081/repository/airdoc-release/")
            isAllowInsecureProtocol = true
        }
        maven {
            //MQTT 通信
            url = uri("https://maven.aliyun.com/nexus/content/repositories/releases/")
        }
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
        maven { url = uri("https://jitpack.io") }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        // AliRepo 阿里仓库服务 https://maven.aliyun.com/mvn/view
        maven {
            url = uri("https://maven.aliyun.com/repository/public")
        }
        maven {
            url = uri("https://maven.aliyun.com/repository/google")
        }
        maven {
            url = uri("https://maven.aliyun.com/repository/gradle-plugin")
        }
        maven {
            url = uri("http://10.1.3.144:8081/repository/airdoc-snapshot/")
            isAllowInsecureProtocol = true
        }
        maven {
            url = uri("http://10.1.3.144:8081/repository/airdoc-release/")
            isAllowInsecureProtocol = true
        }
        maven {
            //MQTT 通信
            url = uri("https://maven.aliyun.com/nexus/content/repositories/releases/")
        }
        google()
        mavenCentral()
        maven { url = uri("https://jitpack.io") }
    }
}

rootProject.name = "kotlin"
include(":app")
include(":LibBehaviorGuidance")
include(":LibMedia")
